"use strict";
'use client';

/* eslint-disable */
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Commands = void 0;
var _codegenNativeComponent = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));
var _codegenNativeCommands = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeCommands"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const Commands = exports.Commands = (0, _codegenNativeCommands.default)({
  supportedCommands: ['blur', 'focus', 'clearText', 'toggleCancelButton', 'setText', 'cancelSearch']
});
var _default = exports.default = (0, _codegenNativeComponent.default)('RNSSearchBar', {});
//# sourceMappingURL=SearchBarNativeComponent.js.map