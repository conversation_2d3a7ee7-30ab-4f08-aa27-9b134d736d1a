{"name": "@react-navigation/routers", "description": "Routers to help build custom navigators", "version": "7.4.1", "keywords": ["react", "react-native", "react-navigation"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/routers"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/custom-routers/", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.tsx", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"nanoid": "^3.3.11"}, "devDependencies": {"@jest/globals": "^30.0.0", "del-cli": "^6.0.0", "react-native-builder-bob": "^0.40.12", "typescript": "^5.8.3"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "27560ad2fc55987654a2f9419a016225387e78fe"}