{"name": "@react-navigation/core", "description": "Core utilities for building navigators", "version": "7.12.1", "keywords": ["react", "react-native", "react-navigation"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.tsx", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/routers": "^7.4.1", "escape-string-regexp": "^4.0.0", "nanoid": "^3.3.11", "query-string": "^7.1.3", "react-is": "^19.1.0", "use-latest-callback": "^0.2.4", "use-sync-external-store": "^1.5.0"}, "devDependencies": {"@jest/globals": "^30.0.0", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "@types/react-is": "^19.0.0", "@types/use-sync-external-store": "^1.5.0", "del-cli": "^6.0.0", "immer": "^10.1.1", "react": "19.0.0", "react-native-builder-bob": "^0.40.12", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">= 18.2.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "c059915c187967137f33b8931e6badcb0c604492"}