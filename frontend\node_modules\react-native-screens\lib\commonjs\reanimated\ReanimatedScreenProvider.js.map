{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_Screen", "_ReanimatedNativeStackScreen", "_ReanimatedScreen", "e", "__esModule", "default", "_extends", "Object", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ReanimatedScreenWrapper", "React", "Component", "ref", "setNativeProps", "props", "setRef", "onComponentRef", "render", "ReanimatedScreen", "isNativeStack", "ReanimatedNativeStackScreen", "AnimatedScreen", "createElement", "ReanimatedScreenProvider", "ScreenContext", "Provider", "value", "children"], "sourceRoot": "../../../src", "sources": ["reanimated/ReanimatedScreenProvider.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAEA,IAAAE,4BAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,iBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAgD,SAAAD,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAP,CAAA,MAAAA,CAAA,GAAAQ,SAAA,CAAAC,MAAA,EAAAT,CAAA,UAAAU,CAAA,GAAAF,SAAA,CAAAR,CAAA,YAAAW,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAJ,QAAA,CAAAW,KAAA,OAAAN,SAAA;AAEhD,MAAMO,uBAAuB,SAASC,cAAK,CAACC,SAAS,CAAc;EACzDC,GAAG,GAAyC,IAAI;EAExDC,cAAcA,CAACC,KAAkB,EAAQ;IACvC,IAAI,CAACF,GAAG,EAAEC,cAAc,CAACC,KAAK,CAAC;EACjC;EAEAC,MAAM,GAAIH,GAAyC,IAAW;IAC5D,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,KAAK,CAACE,cAAc,GAAGJ,GAAG,CAAC;EAClC,CAAC;EAEDK,MAAMA,CAAA,EAAG;IACP,MAAMC,gBAAgB,GAAG,IAAI,CAACJ,KAAK,CAACK,aAAa,GAC7CC,oCAA2B,GAC3BC,yBAAc;IAClB,oBACEjC,MAAA,CAAAQ,OAAA,CAAA0B,aAAA,CAACJ,gBAAgB,EAAArB,QAAA,KACX,IAAI,CAACiB,KAAK;MACd;MACAF,GAAG,EAAE,IAAI,CAACG;IAAO,EAClB,CAAC;EAEN;AACF;AAEe,SAASQ,wBAAwBA,CAC9CT,KAAiC,EACjC;EACA;IAAA;IACE;IACA1B,MAAA,CAAAQ,OAAA,CAAA0B,aAAA,CAAC/B,OAAA,CAAAiC,aAAa,CAACC,QAAQ;MAACC,KAAK,EAAEjB;IAA+B,GAC3DK,KAAK,CAACa,QACe;EAAC;AAE7B", "ignoreList": []}