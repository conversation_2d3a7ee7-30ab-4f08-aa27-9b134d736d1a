{"version": 3, "names": ["React", "ReactNativeWeb", "Option", "props", "unstable_createElement", "PickerItem", "color", "label", "testID", "value", "enabled", "createElement", "disabled", "undefined", "style"], "sourceRoot": "../../js", "sources": ["PickerItem.js"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,cAAc,MAAM,kBAAkB;AAUlD,MAAMC,MAAM,GAAIC,KAAU,IACxBF,cAAc,CAACG,sBAAsB,CAAC,QAAQ,EAAED,KAAK,CAAC;;AAExD;AACA;AACA;AACA;AACA,eAAe,SAASE,UAAUA,CAAC;EACjCC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,OAAO,GAAG;AACL,CAAC,EAAc;EACpB,oBACEV,KAAA,CAAAW,aAAA,CAACT,MAAM;IACLU,QAAQ,EAAEF,OAAO,KAAK,KAAK,GAAG,IAAI,GAAGG,SAAU;IAC/CC,KAAK,EAAE;MAACR;IAAK,CAAE;IACfE,MAAM,EAAEA,MAAO;IACfC,KAAK,EAAEA,KAAM;IACbF,KAAK,EAAEA;EAAM,GACZA,KACK,CAAC;AAEb"}