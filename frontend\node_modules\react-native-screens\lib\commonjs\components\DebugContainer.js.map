{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_AppContainer", "_interopRequireDefault", "_ScreenContentWrapper", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "DebugContainer", "props", "createElement", "process", "env", "NODE_ENV", "stackPresentation", "rest", "Platform", "OS", "displayName", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/DebugContainer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,aAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAD,sBAAA,CAAAH,OAAA;AAA0D,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAH1D;;AAWA;AACA;AACA;AACA;AACA;AACA,IAAIW,cAAmD,GAAGC,KAAK,IAAI;EACjE,oBAAO5B,KAAA,CAAA6B,aAAA,CAACvB,qBAAA,CAAAG,OAAoB,EAAKmB,KAAQ,CAAC;AAC5C,CAAC;AAED,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,cAAc,GAAIC,KAAqB,IAAK;IAC1C,MAAM;MAAEK,iBAAiB;MAAE,GAAGC;IAAK,CAAC,GAAGN,KAAK;IAE5C,IACEO,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrBH,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,WAAW,EACjC;MACA;MACA,oBACEjC,KAAA,CAAA6B,aAAA,CAACzB,aAAA,CAAAK,OAAY,qBACXT,KAAA,CAAA6B,aAAA,CAACvB,qBAAA,CAAAG,OAAoB,EAAKyB,IAAO,CACrB,CAAC;IAEnB;IAEA,oBAAOlC,KAAA,CAAA6B,aAAA,CAACvB,qBAAA,CAAAG,OAAoB,EAAKyB,IAAO,CAAC;EAC3C,CAAC;EAEDP,cAAc,CAACU,WAAW,GAAG,gBAAgB;AAC/C;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA9B,OAAA,GAEckB,cAAc", "ignoreList": []}