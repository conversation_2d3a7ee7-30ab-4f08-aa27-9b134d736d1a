"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = _interopRequireDefault(require("react"));
var _reactNative = require("react-native");
var _SplitViewScreenNativeComponent = _interopRequireDefault(require("../../fabric/gamma/SplitViewScreenNativeComponent"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
/**
 * EXPERIMENTAL API, MIGHT CHANGE W/O ANY NOTICE
 */
function SplitViewScreen({
  children
}) {
  return /*#__PURE__*/_react.default.createElement(_SplitViewScreenNativeComponent.default, {
    style: _reactNative.StyleSheet.absoluteFill
  }, children);
}
var _default = exports.default = SplitViewScreen;
//# sourceMappingURL=SplitViewScreen.js.map