import React, { useEffect, useState } from "react";
import { View, Text, ActivityIndicator, Button } from "react-native";
import { useRoute, useNavigation } from "@react-navigation/native";
import { API_ENDPOINTS, apiRequest } from "../config/api";

export default function DonorDetailScreen() {
  const route = useRoute<any>();
  const navigation = useNavigation();
  const { id } = route.params || {};
  const [donor, setDonor] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (!id) return;

    const fetchDonor = async () => {
      setLoading(true);
      try {
        const data = await apiRequest(API_ENDPOINTS.DONOR_BY_ID(id));
        setDonor(data);
      } catch (error) {
        console.error('Error fetching donor:', error);
        setError("Donor not found");
      } finally {
        setLoading(false);
      }
    };

    fetchDonor();
  }, [id]);

  if (loading) return <ActivityIndicator />;
  if (error) return <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}><Text>{error}</Text><Button title="Back" onPress={() => navigation.goBack()} /></View>;
  if (!donor) return null;

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <Button title="Back" onPress={() => navigation.goBack()} />
      <Text style={{ fontWeight: "bold", fontSize: 24 }}>{donor.firstName} {donor.lastName}</Text>
      <Text>Blood Group: {donor.bloodGroup}</Text>
      <Text>Division ID: {donor.divisionId}</Text>
      <Text>Zila ID: {donor.zilaId}</Text>
      <Text>Upazila ID: {donor.upazilaId}</Text>
      <Text>Village: {donor.village}</Text>
      <Text>Current Location: {donor.currentLocation}</Text>
      <Text>Last Donation Date: {donor.lastDonationDate}</Text>
      <Text>Phone: {donor.phoneNumber}</Text>
      <Text>Available: {donor.isAvailable ? "Yes" : "No"}</Text>
      <Text>Notes: {donor.notes}</Text>
      <Text>Created At: {donor.createdAt}</Text>
      <Text>Updated At: {donor.updatedAt}</Text>
    </View>
  );
} 