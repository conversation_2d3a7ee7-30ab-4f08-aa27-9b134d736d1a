{"version": 3, "names": ["React", "AnimatedHeaderHeightContext", "createContext", "undefined", "useAnimatedHeaderHeight", "animatedValue", "useContext", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useAnimatedHeaderHeight.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,OAAO,MAAMC,2BAA2B,gBAAGD,KAAK,CAACE,aAAa,CAE5DC,SAAS,CAAC;AAEZ,OAAO,SAASC,uBAAuBA,CAAA,EAAG;EACxC,MAAMC,aAAa,GAAGL,KAAK,CAACM,UAAU,CAACL,2BAA2B,CAAC;EAEnE,IAAII,aAAa,KAAKF,SAAS,EAAE;IAC/B,MAAM,IAAII,KAAK,CACb,uFACF,CAAC;EACH;EAEA,OAAOF,aAAa;AACtB", "ignoreList": []}