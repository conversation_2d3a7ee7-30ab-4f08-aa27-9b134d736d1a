{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_warnOnce", "_interopRequireDefault", "_DebugContainer", "_ScreenStackHeaderConfig", "_Screen", "_ScreenStack", "_contexts", "_ScreenFooter", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "ScreenStackItem", "children", "headerConfig", "activityState", "shouldFreeze", "stackPresentation", "sheetAllowedDetents", "contentStyle", "style", "screenId", "unstable_sheetFooter", "rest", "ref", "currentScreenRef", "useRef", "screenRefs", "useContext", "RNSScreensRefContext", "useImperativeHandle", "current", "isHeaderInModal", "Platform", "OS", "hidden", "headerHiddenPreviousRef", "useEffect", "warnOnce", "content", "createElement", "Fragment", "styles", "absolute", "container", "ScreenStackHeaderConfig", "FooterComponent", "internalScreenStyle", "flattenContentStyles", "StyleSheet", "flatten", "backgroundColor", "node", "console", "warn", "currentRefs", "enabled", "isNativeStack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "largeTitle", "absoluteFill", "_default", "exports", "forwardRef", "create", "flex", "position", "top", "start", "end"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackItem.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,eAAA,GAAAD,sBAAA,CAAAH,OAAA;AAEA,IAAAK,wBAAA,GAAAL,OAAA;AACA,IAAAM,OAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,YAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,SAAA,GAAAR,OAAA;AACA,IAAAS,aAAA,GAAAT,OAAA;AAAiD,SAAAG,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAX,wBAAAW,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAT,CAAA,MAAAA,CAAA,GAAAuB,SAAA,CAAAC,MAAA,EAAAxB,CAAA,UAAAM,CAAA,GAAAiB,SAAA,CAAAvB,CAAA,YAAAK,CAAA,IAAAC,CAAA,OAAAU,cAAA,CAAAC,IAAA,CAAAX,CAAA,EAAAD,CAAA,MAAAI,CAAA,CAAAJ,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAI,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAWjD,SAASG,eAAeA,CACtB;EACEC,QAAQ;EACRC,YAAY;EACZC,aAAa;EACbC,YAAY;EACZC,iBAAiB;EACjBC,mBAAmB;EACnBC,YAAY;EACZC,KAAK;EACLC,QAAQ;EACR;EACAC,oBAAoB;EACpB,GAAGC;AACE,CAAC,EACRC,GAA6B,EAC7B;EACA,MAAMC,gBAAgB,GAAGnD,KAAK,CAACoD,MAAM,CAAc,IAAI,CAAC;EACxD,MAAMC,UAAU,GAAGrD,KAAK,CAACsD,UAAU,CAACC,8BAAoB,CAAC;EAEzDvD,KAAK,CAACwD,mBAAmB,CAACN,GAAG,EAAE,MAAMC,gBAAgB,CAACM,OAAQ,CAAC;EAE/D,MAAMC,eAAe,GACnBC,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB,KAAK,GACLjB,iBAAiB,KAAK,MAAM,IAAIH,YAAY,EAAEqB,MAAM,KAAK,KAAK;EAEpE,MAAMC,uBAAuB,GAAG9D,KAAK,CAACoD,MAAM,CAACZ,YAAY,EAAEqB,MAAM,CAAC;EAElE7D,KAAK,CAAC+D,SAAS,CAAC,MAAM;IACpB,IAAAC,iBAAQ,EACNL,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACvBjB,iBAAiB,KAAK,MAAM,IAC5BmB,uBAAuB,CAACL,OAAO,KAAKjB,YAAY,EAAEqB,MAAM,EAC1D,qHACF,CAAC;IAEDC,uBAAuB,CAACL,OAAO,GAAGjB,YAAY,EAAEqB,MAAM;EACxD,CAAC,EAAE,CAACrB,YAAY,EAAEqB,MAAM,EAAElB,iBAAiB,CAAC,CAAC;EAE7C,MAAMsB,OAAO,gBACXjE,KAAA,CAAAkE,aAAA,CAAAlE,KAAA,CAAAmE,QAAA,qBACEnE,KAAA,CAAAkE,aAAA,CAAC5D,eAAA,CAAAQ,OAAc;IACbgC,KAAK,EAAE,CACLH,iBAAiB,KAAK,WAAW,GAC7BgB,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACnBQ,MAAM,CAACC,QAAQ,GACfzB,mBAAmB,KAAK,eAAe,GACvC,IAAI,GACJwB,MAAM,CAACE,SAAS,GAClBF,MAAM,CAACE,SAAS,EACpBzB,YAAY,CACZ;IACFF,iBAAiB,EAAEA,iBAAiB,IAAI;EAAO,GAC9CJ,QACa,CAAC,eAYjBvC,KAAA,CAAAkE,aAAA,CAAC3D,wBAAA,CAAAgE,uBAAuB,EAAK/B,YAAe,CAAC,EAE5CG,iBAAiB,KAAK,WAAW,IAAIK,oBAAoB,iBACxDhD,KAAA,CAAAkE,aAAA,CAACvD,aAAA,CAAA6D,eAAe,QAAExB,oBAAoB,CAAC,CAAmB,CAE5D,CACH;;EAED;EACA;EACA;EACA,IAAIyB,mBAAmB;EAEvB,IAAI9B,iBAAiB,KAAK,WAAW,IAAIE,YAAY,EAAE;IACrD,MAAM6B,oBAAoB,GAAGC,uBAAU,CAACC,OAAO,CAAC/B,YAAY,CAAC;IAC7D4B,mBAAmB,GAAG;MACpBI,eAAe,EAAEH,oBAAoB,EAAEG;IACzC,CAAC;EACH;EAEA,oBACE7E,KAAA,CAAAkE,aAAA,CAAC1D,OAAA,CAAAM,OAAM,EAAAkB,QAAA;IACLkB,GAAG,EAAE4B,IAAI,IAAI;MACX3B,gBAAgB,CAACM,OAAO,GAAGqB,IAAI;MAE/B,IAAIzB,UAAU,KAAK,IAAI,EAAE;QACvB0B,OAAO,CAACC,IAAI,CACV,kGACF,CAAC;QACD;MACF;MAEA,MAAMC,WAAW,GAAG5B,UAAU,CAACI,OAAO;MAEtC,IAAIqB,IAAI,KAAK,IAAI,EAAE;QACjB;QACA,OAAOG,WAAW,CAAClC,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACLkC,WAAW,CAAClC,QAAQ,CAAC,GAAG;UAAEU,OAAO,EAAEqB;QAAK,CAAC;MAC3C;IACF,CAAE;IACFI,OAAO;IACPC,aAAa;IACb1C,aAAa,EAAEA,aAAc;IAC7BC,YAAY,EAAEA,YAAa;IAC3BK,QAAQ,EAAEA,QAAS;IACnBJ,iBAAiB,EAAEA,iBAAkB;IACrCyC,cAAc,EAAE5C,YAAY,EAAE6C,UAAU,IAAI,KAAM;IAClDzC,mBAAmB,EAAEA,mBAAoB;IACzCE,KAAK,EAAE,CAACA,KAAK,EAAE2B,mBAAmB;EAAE,GAChCxB,IAAI,GACPS,eAAe,gBACd1D,KAAA,CAAAkE,aAAA,CAACzD,YAAA,CAAAK,OAAW;IAACgC,KAAK,EAAEsB,MAAM,CAACE;EAAU,gBACnCtE,KAAA,CAAAkE,aAAA,CAAC1D,OAAA,CAAAM,OAAM;IACLoE,OAAO;IACPC,aAAa;IACb1C,aAAa,EAAEA,aAAc;IAC7BC,YAAY,EAAEA,YAAa;IAC3B0C,cAAc,EAAE5C,YAAY,EAAE6C,UAAU,IAAI,KAAM;IAClDvC,KAAK,EAAE6B,uBAAU,CAACW;EAAa,GAC9BrB,OACK,CACG,CAAC,GAEdA,OAEI,CAAC;AAEb;AAAC,IAAAsB,QAAA,GAAAC,OAAA,CAAA1E,OAAA,gBAEcd,KAAK,CAACyF,UAAU,CAACnD,eAAe,CAAC;AAEhD,MAAM8B,MAAM,GAAGO,uBAAU,CAACe,MAAM,CAAC;EAC/BpB,SAAS,EAAE;IACTqB,IAAI,EAAE;EACR,CAAC;EACDtB,QAAQ,EAAE;IACRuB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}