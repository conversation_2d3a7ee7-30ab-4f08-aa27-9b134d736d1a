{"name": "@react-navigation/native", "description": "React Native integration for React Navigation", "version": "7.1.14", "keywords": ["react-native", "react-navigation", "ios", "android"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/native"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.tsx", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/core": "^7.12.1", "escape-string-regexp": "^4.0.0", "fast-deep-equal": "^3.1.3", "nanoid": "^3.3.11", "use-latest-callback": "^0.2.4"}, "devDependencies": {"@jest/globals": "^30.0.0", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "@types/react-dom": "~19.1.3", "del-cli": "^6.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-builder-bob": "^0.40.12", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">= 18.2.0", "react-native": "*"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "c059915c187967137f33b8931e6badcb0c604492"}