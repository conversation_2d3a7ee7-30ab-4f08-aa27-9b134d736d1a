"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _codegenNativeComponent = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
// eslint-disable-next-line @typescript-eslint/ban-types
var _default = exports.default = (0, _codegenNativeComponent.default)('RNSScreenStack', {});
//# sourceMappingURL=ScreenStackNativeComponent.js.map