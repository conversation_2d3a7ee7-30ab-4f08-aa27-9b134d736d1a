{"version": 3, "names": ["usePreventRemoveContext", "React", "useInvalidPreventRemoveError", "descriptors", "preventedRoutes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "preventedDescriptor", "isHeaderBackButtonMenuEnabledOnPreventedScreen", "options", "headerBackButtonMenuEnabled", "preventedRouteName", "route", "name", "useEffect", "message", "console", "error"], "sourceRoot": "../../../src", "sources": ["utils/useInvalidPreventRemoveError.tsx"], "mappings": ";;AAAA,SAASA,uBAAuB,QAAQ,0BAA0B;AAClE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAI9B,OAAO,SAASC,4BAA4BA,CAC1CC,WAAqC,EACrC;EACA,MAAM;IAAEC;EAAgB,CAAC,GAAGJ,uBAAuB,CAAC,CAAC;EACrD,MAAMK,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC,CAAC;EACzD,MAAMI,mBAAmB,GAAGL,WAAW,CAACE,iBAAiB,CAAC;EAC1D,MAAMI,8CAA8C,GAClDD,mBAAmB,EAAEE,OAAO,EAAEC,2BAA2B;EAC3D,MAAMC,kBAAkB,GAAGJ,mBAAmB,EAAEK,KAAK,EAAEC,IAAI;EAE3Db,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,IACEV,iBAAiB,IAAI,IAAI,IACzBI,8CAA8C,EAC9C;MACA,MAAMO,OAAO,GACX,cAAcJ,kBAAkB,2GAA2G,GAC3I,8DAA8DA,kBAAkB,mCAAmC;MACrHK,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;EACF,CAAC,EAAE,CACDX,iBAAiB,EACjBI,8CAA8C,EAC9CG,kBAAkB,CACnB,CAAC;AACJ", "ignoreList": []}