{"version": 3, "names": ["createNavigatorFactory", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "NativeStackView", "jsx", "_jsx", "NativeStackNavigator", "id", "initialRouteName", "children", "layout", "screenListeners", "screenOptions", "screenLayout", "UNSTABLE_router", "rest", "state", "describe", "descriptors", "navigation", "NavigationContent", "useEffect", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "popToTop", "target", "key", "createNativeStackNavigator", "config"], "sourceRoot": "../../../src", "sources": ["navigators/createNativeStackNavigator.tsx"], "mappings": ";;AAAA,SACEA,sBAAsB,EAKtBC,YAAY,EAEZC,WAAW,EAIXC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAQ9B,SAASC,eAAe,QAAQ,0BAA0B;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE3D,SAASC,oBAAoBA,CAAC;EAC5BC,EAAE;EACFC,gBAAgB;EAChBC,QAAQ;EACRC,MAAM;EACNC,eAAe;EACfC,aAAa;EACbC,YAAY;EACZC,eAAe;EACf,GAAGC;AACsB,CAAC,EAAE;EAC5B,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACnEnB,oBAAoB,CAMlBD,WAAW,EAAE;IACbO,EAAE;IACFC,gBAAgB;IAChBC,QAAQ;IACRC,MAAM;IACNC,eAAe;IACfC,aAAa;IACbC,YAAY;IACZC;EACF,CAAC,CAAC;EAEJZ,KAAK,CAACmB,SAAS,CACb;EACE;EACAF,UAAU,EAAEG,WAAW,GAAG,UAAU,EAAGC,CAAM,IAAK;IAChD,MAAMC,SAAS,GAAGL,UAAU,CAACK,SAAS,CAAC,CAAC;;IAExC;IACA;IACAC,qBAAqB,CAAC,MAAM;MAC1B,IACET,KAAK,CAACU,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAAgCI,gBAAgB,EACnD;QACA;QACA;QACAR,UAAU,CAACS,QAAQ,CAAC;UAClB,GAAG7B,YAAY,CAAC8B,QAAQ,CAAC,CAAC;UAC1BC,MAAM,EAAEd,KAAK,CAACe;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EACJ,CAACZ,UAAU,EAAEH,KAAK,CAACU,KAAK,EAAEV,KAAK,CAACe,GAAG,CACrC,CAAC;EAED,oBACE1B,IAAA,CAACe,iBAAiB;IAAAX,QAAA,eAChBJ,IAAA,CAACF,eAAe;MAAA,GACVY,IAAI;MACRC,KAAK,EAAEA,KAAM;MACbG,UAAU,EAAEA,UAAW;MACvBD,WAAW,EAAEA,WAAY;MACzBD,QAAQ,EAAEA;IAAS,CACpB;EAAC,CACe,CAAC;AAExB;AAEA,OAAO,SAASe,0BAA0BA,CAmBxCC,MAAe,EAAmC;EAClD,OAAOnC,sBAAsB,CAACQ,oBAAoB,CAAC,CAAC2B,MAAM,CAAC;AAC7D", "ignoreList": []}