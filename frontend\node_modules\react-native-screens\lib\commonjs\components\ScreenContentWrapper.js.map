{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_ScreenContentWrapperNativeComponent", "e", "__esModule", "default", "_extends", "Object", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ScreenContentWrapper", "props", "createElement", "collapsable", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/ScreenContentWrapper.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,oCAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAgG,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAP,CAAA,MAAAA,CAAA,GAAAQ,SAAA,CAAAC,MAAA,EAAAT,CAAA,UAAAU,CAAA,GAAAF,SAAA,CAAAR,CAAA,YAAAW,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAJ,QAAA,CAAAW,KAAA,OAAAN,SAAA;AAEhG,SAASO,oBAAoBA,CAACC,KAAgB,EAAE;EAC9C,oBAAOpB,MAAA,CAAAM,OAAA,CAAAe,aAAA,CAAClB,oCAAA,CAAAG,OAAmC,EAAAC,QAAA;IAACe,WAAW,EAAE;EAAM,GAAKF,KAAK,CAAG,CAAC;AAC/E;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAlB,OAAA,GAEca,oBAAoB", "ignoreList": []}