{"version": 3, "names": ["_helpers", "require", "_RCTAsyncStorage", "_interopRequireDefault", "e", "__esModule", "default", "RCTAsyncStorage", "Error", "AsyncStorage", "_getRequests", "_get<PERSON>eys", "_immediate", "getItem", "key", "callback", "Promise", "resolve", "reject", "checkValidInput", "multiGet", "errors", "result", "value", "errs", "convertErrors", "setItem", "multiSet", "removeItem", "multiRemove", "mergeItem", "multiMerge", "clear", "error", "err", "convertError", "getAllKeys", "keys", "flushGetRequests", "getRequests", "get<PERSON><PERSON><PERSON>", "map", "for<PERSON>ach", "reqL<PERSON>th", "length", "errorList", "i", "request", "requestResult", "setImmediate", "getRequest", "keyIndex", "promiseResult", "push", "indexOf", "keyValuePairs", "checkValidArgs", "_default", "exports"], "sourceRoot": "../../src", "sources": ["AsyncStorage.native.ts"], "mappings": ";;;;;;AAOA,IAAAA,QAAA,GAAAC,OAAA;AAMA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAgD,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAbhD;AACA;AACA;AACA;AACA;AACA;;AAgBA,IAAI,CAACG,wBAAe,EAAE;EACpB,MAAM,IAAIC,KAAK,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,CAAC,MAA0B;EAC9C,IAAIC,YAA4B,GAAG,EAAE;EACrC,IAAIC,QAAkB,GAAG,EAAE;EAC3B,IAAIC,UAAkD,GAAG,IAAI;EAE7D,OAAO;IACL;AACJ;AACA;AACA;AACA;IACIC,OAAO,EAAEA,CAACC,GAAG,EAAEC,QAAQ,KAAK;MAC1B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,IAAAC,wBAAe,EAACL,GAAG,CAAC;QACpBP,wBAAe,CAACa,QAAQ,CACtB,CAACN,GAAG,CAAC,EACL,CAACO,MAAoB,EAAEC,MAAmB,KAAK;UAC7C;UACA,MAAMC,KAAK,GAAGD,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;UACpD,MAAME,IAAI,GAAG,IAAAC,sBAAa,EAACJ,MAAM,CAAC;UAClCN,QAAQ,GAAGS,IAAI,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC;UAC5B,IAAIC,IAAI,EAAE;YACRN,MAAM,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACLP,OAAO,CAACM,KAAK,CAAC;UAChB;QACF,CACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIG,OAAO,EAAEA,CAACZ,GAAG,EAAES,KAAK,EAAER,QAAQ,KAAK;MACjC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,IAAAC,wBAAe,EAACL,GAAG,EAAES,KAAK,CAAC;QAC3BhB,wBAAe,CAACoB,QAAQ,CAAC,CAAC,CAACb,GAAG,EAAES,KAAK,CAAC,CAAC,EAAGF,MAAoB,IAAK;UACjE,MAAMG,IAAI,GAAG,IAAAC,sBAAa,EAACJ,MAAM,CAAC;UAClCN,QAAQ,GAAGS,IAAI,GAAG,CAAC,CAAC,CAAC;UACrB,IAAIA,IAAI,EAAE;YACRN,MAAM,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACLP,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIW,UAAU,EAAEA,CAACd,GAAG,EAAEC,QAAQ,KAAK;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,IAAAC,wBAAe,EAACL,GAAG,CAAC;QACpBP,wBAAe,CAACsB,WAAW,CAAC,CAACf,GAAG,CAAC,EAAGO,MAAoB,IAAK;UAC3D,MAAMG,IAAI,GAAG,IAAAC,sBAAa,EAACJ,MAAM,CAAC;UAClCN,QAAQ,GAAGS,IAAI,GAAG,CAAC,CAAC,CAAC;UACrB,IAAIA,IAAI,EAAE;YACRN,MAAM,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACLP,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIa,SAAS,EAAEA,CAAChB,GAAG,EAAES,KAAK,EAAER,QAAQ,KAAK;MACnC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,IAAAC,wBAAe,EAACL,GAAG,EAAES,KAAK,CAAC;QAC3BhB,wBAAe,CAACwB,UAAU,CAAC,CAAC,CAACjB,GAAG,EAAES,KAAK,CAAC,CAAC,EAAGF,MAAoB,IAAK;UACnE,MAAMG,IAAI,GAAG,IAAAC,sBAAa,EAACJ,MAAM,CAAC;UAClCN,QAAQ,GAAGS,IAAI,GAAG,CAAC,CAAC,CAAC;UACrB,IAAIA,IAAI,EAAE;YACRN,MAAM,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACLP,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACIe,KAAK,EAAGjB,QAAQ,IAAK;MACnB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCX,wBAAe,CAACyB,KAAK,CAAEC,KAAiB,IAAK;UAC3C,MAAMC,GAAG,GAAG,IAAAC,qBAAY,EAACF,KAAK,CAAC;UAC/BlB,QAAQ,GAAGmB,GAAG,CAAC;UACf,IAAIA,GAAG,EAAE;YACPhB,MAAM,CAACgB,GAAG,CAAC;UACb,CAAC,MAAM;YACLjB,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACImB,UAAU,EAAGrB,QAAQ,IAAK;MACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCX,wBAAe,CAAC6B,UAAU,CAAC,CAACH,KAAiB,EAAEI,IAAe,KAAK;UACjE,MAAMH,GAAG,GAAG,IAAAC,qBAAY,EAACF,KAAK,CAAC;UAC/BlB,QAAQ,GAAGmB,GAAG,EAAEG,IAAI,CAAC;UACrB,IAAIA,IAAI,EAAE;YACRpB,OAAO,CAACoB,IAAI,CAAC;UACf,CAAC,MAAM;YACLnB,MAAM,CAACgB,GAAG,CAAC;UACb;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEI;AACJ;AACA;AACA;AACA;IACII,gBAAgB,EAAEA,CAAA,KAAM;MACtB,MAAMC,WAAW,GAAG7B,YAAY;MAChC,MAAM8B,OAAO,GAAG7B,QAAQ;MAExBD,YAAY,GAAG,EAAE;MACjBC,QAAQ,GAAG,EAAE;MAEbJ,wBAAe,CAACa,QAAQ,CACtBoB,OAAO,EACP,CAACnB,MAAoB,EAAEC,MAAmB,KAAK;QAC7C;QACA;QACA;QACA;QACA;QACA;QACA,MAAMmB,GAA2B,GAAG,CAAC,CAAC;QACtCnB,MAAM,EAAEoB,OAAO,CAAC,CAAC,CAAC5B,GAAG,EAAES,KAAK,CAAC,KAAK;UAChCkB,GAAG,CAAC3B,GAAG,CAAC,GAAGS,KAAK;UAChB,OAAOA,KAAK;QACd,CAAC,CAAC;QACF,MAAMoB,SAAS,GAAGJ,WAAW,CAACK,MAAM;;QAEpC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACU,MAAMC,SAAS,GAAG,IAAApB,sBAAa,EAACJ,MAAM,CAAC;QACvC,MAAMY,KAAK,GAAGY,SAAS,EAAED,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;QAErD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,EAAEG,CAAC,EAAE,EAAE;UAClC,MAAMC,OAAO,GAAGR,WAAW,CAACO,CAAC,CAAC;UAC9B,IAAIb,KAAK,EAAE;YACTc,OAAO,CAAChC,QAAQ,GAAG8B,SAAS,CAAC;YAC7BE,OAAO,CAAC7B,MAAM,GAAGe,KAAK,CAAC;YACvB;UACF;UACA,MAAMe,aAAa,GAAGD,OAAO,CAACV,IAAI,CAACI,GAAG,CAAgB3B,GAAG,IAAK,CAC5DA,GAAG,EACH2B,GAAG,CAAC3B,GAAG,CAAC,CACT,CAAC;UACFiC,OAAO,CAAChC,QAAQ,GAAG,IAAI,EAAEiC,aAAa,CAAC;UACvCD,OAAO,CAAC9B,OAAO,GAAG+B,aAAa,CAAC;QAClC;MACF,CACF,CAAC;IACH,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACI5B,QAAQ,EAAEA,CAACiB,IAAI,EAAEtB,QAAQ,KAAK;MAC5B,IAAI,CAACH,UAAU,EAAE;QACfA,UAAU,GAAGqC,YAAY,CAAC,MAAM;UAC9BrC,UAAU,GAAG,IAAI;UACjBH,YAAY,CAAC6B,gBAAgB,CAAC,CAAC;QACjC,CAAC,CAAC;MACJ;MAEA,MAAMY,UAAwB,GAAG;QAC/Bb,IAAI,EAAEA,IAAI;QACVtB,QAAQ,EAAEA,QAAQ;QAClB;QACAoC,QAAQ,EAAExC,QAAQ,CAACiC;MACrB,CAAC;MAED,MAAMQ,aAAa,GAAG,IAAIpC,OAAO,CAC/B,CAACC,OAAO,EAAEC,MAAM,KAAK;QACnBgC,UAAU,CAACjC,OAAO,GAAGA,OAAO;QAC5BiC,UAAU,CAAChC,MAAM,GAAGA,MAAM;MAC5B,CACF,CAAC;MAEDR,YAAY,CAAC2C,IAAI,CAACH,UAAU,CAAC;MAC7B;MACAb,IAAI,CAACK,OAAO,CAAE5B,GAAG,IAAK;QACpB,IAAIH,QAAQ,CAAC2C,OAAO,CAACxC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UAChCH,QAAQ,CAAC0C,IAAI,CAACvC,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;MAEF,OAAOsC,aAAa;IACtB,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIzB,QAAQ,EAAEA,CAAC4B,aAAa,EAAExC,QAAQ,KAAK;MACrC,IAAAyC,uBAAc,EAACD,aAAa,EAAExC,QAAQ,CAAC;MACvC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCqC,aAAa,CAACb,OAAO,CAAC,CAAC,CAAC5B,GAAG,EAAES,KAAK,CAAC,KAAK;UACtC,IAAAJ,wBAAe,EAACL,GAAG,EAAES,KAAK,CAAC;QAC7B,CAAC,CAAC;QAEFhB,wBAAe,CAACoB,QAAQ,CAAC4B,aAAa,EAAGlC,MAAoB,IAAK;UAChE,MAAMY,KAAK,GAAG,IAAAR,sBAAa,EAACJ,MAAM,CAAC;UACnCN,QAAQ,GAAGkB,KAAK,CAAC;UACjB,IAAIA,KAAK,EAAE;YACTf,MAAM,CAACe,KAAK,CAAC;UACf,CAAC,MAAM;YACLhB,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIY,WAAW,EAAEA,CAACQ,IAAI,EAAEtB,QAAQ,KAAK;MAC/B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCmB,IAAI,CAACK,OAAO,CAAE5B,GAAG,IAAK,IAAAK,wBAAe,EAACL,GAAG,CAAC,CAAC;QAE3CP,wBAAe,CAACsB,WAAW,CAACQ,IAAI,EAAGhB,MAAoB,IAAK;UAC1D,MAAMY,KAAK,GAAG,IAAAR,sBAAa,EAACJ,MAAM,CAAC;UACnCN,QAAQ,GAAGkB,KAAK,CAAC;UACjB,IAAIA,KAAK,EAAE;YACTf,MAAM,CAACe,KAAK,CAAC;UACf,CAAC,MAAM;YACLhB,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIc,UAAU,EAAEA,CAACwB,aAAa,EAAExC,QAAQ,KAAK;MACvC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCX,wBAAe,CAACwB,UAAU,CAACwB,aAAa,EAAGlC,MAAoB,IAAK;UAClE,MAAMY,KAAK,GAAG,IAAAR,sBAAa,EAACJ,MAAM,CAAC;UACnCN,QAAQ,GAAGkB,KAAK,CAAC;UACjB,IAAIA,KAAK,EAAE;YACTf,MAAM,CAACe,KAAK,CAAC;UACf,CAAC,MAAM;YACLhB,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;AACH,CAAC,EAAE,CAAC;AAAC,IAAAwC,QAAA,GAAAC,OAAA,CAAApD,OAAA,GAEUG,YAAY", "ignoreList": []}