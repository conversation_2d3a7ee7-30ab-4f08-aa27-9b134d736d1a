{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ScreenStackHeaderSubview", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderConfig", "ScreenStackHeaderCenterView", "ScreenStackHeaderBackButtonImage", "_react", "_interopRequireDefault", "require", "_reactNative", "_ScreenStackHeaderConfigNativeComponent", "_ScreenStackHeaderSubviewNativeComponent", "_edgeToEdge", "e", "__esModule", "default", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ScreenStackHeaderSubviewNativeComponent", "React", "forwardRef", "props", "ref", "createElement", "topInsetEnabled", "EDGE_TO_EDGE", "style", "styles", "headerConfig", "pointerEvents", "displayName", "type", "headerSubview", "Image", "resizeMode", "fadeDuration", "rest", "headerSubviewCenter", "StyleSheet", "create", "flexDirection", "alignItems", "justifyContent", "flexShrink", "position", "width", "Platform", "OS", "undefined"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,wBAAA,GAAAF,OAAA,CAAAG,8BAAA,GAAAH,OAAA,CAAAI,0BAAA,GAAAJ,OAAA,CAAAK,yBAAA,GAAAL,OAAA,CAAAM,uBAAA,GAAAN,OAAA,CAAAO,2BAAA,GAAAP,OAAA,CAAAQ,gCAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,uCAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,wCAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAGA,IAAAI,WAAA,GAAAJ,OAAA;AAAsD,SAAAD,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,SAAA,WAAAA,QAAA,GAAArB,MAAA,CAAAsB,MAAA,GAAAtB,MAAA,CAAAsB,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAO,SAAA,CAAAC,MAAA,EAAAR,CAAA,UAAAS,CAAA,GAAAF,SAAA,CAAAP,CAAA,YAAAU,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA,KALtD;AAOO,MAAMrB,wBAAkF,GAAAF,OAAA,CAAAE,wBAAA,GAC7F4B,gDAAuC;AAElC,MAAMxB,uBAAuB,GAAAN,OAAA,CAAAM,uBAAA,gBAAGyB,cAAK,CAACC,UAAU,CAGrD,CAACC,KAAK,EAAEC,GAAG,kBACXzB,MAAA,CAAAS,OAAA,CAAAiB,aAAA,CAACtB,uCAAA,CAAAK,OAAsC,EAAAC,QAAA,KACjCc,KAAK;EACTC,GAAG,EAAEA,GAAI;EACTE,eAAe,EAAEC,wBAAY,GAAG,IAAI,GAAGJ,KAAK,CAACG,eAAgB;EAC7DE,KAAK,EAAEC,MAAM,CAACC,YAAa;EAC3BC,aAAa,EAAC;AAAU,EACzB,CACF,CAAC;AAEFnC,uBAAuB,CAACoC,WAAW,GAAG,yBAAyB;AAExD,MAAMlC,gCAAgC,GAC3CyB,KAAiB,iBAEjBxB,MAAA,CAAAS,OAAA,CAAAiB,aAAA,CAACjC,wBAAwB;EAACyC,IAAI,EAAC,MAAM;EAACL,KAAK,EAAEC,MAAM,CAACK;AAAc,gBAChEnC,MAAA,CAAAS,OAAA,CAAAiB,aAAA,CAACvB,YAAA,CAAAiC,KAAK,EAAA1B,QAAA;EAAC2B,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKd,KAAK,CAAG,CAChC,CAC3B;AAACjC,OAAA,CAAAQ,gCAAA,GAAAA,gCAAA;AAEK,MAAMJ,0BAA0B,GAAI6B,KAAgB,IAAkB;EAC3E,MAAM;IAAEK,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGf,KAAK;EAEhC,oBACExB,MAAA,CAAAS,OAAA,CAAAiB,aAAA,CAACjC,wBAAwB,EAAAiB,QAAA,KACnB6B,IAAI;IACRL,IAAI,EAAC,OAAO;IACZL,KAAK,EAAE,CAACC,MAAM,CAACK,aAAa,EAAEN,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAACtC,OAAA,CAAAI,0BAAA,GAAAA,0BAAA;AAEK,MAAMC,yBAAyB,GAAI4B,KAAgB,IAAkB;EAC1E,MAAM;IAAEK,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGf,KAAK;EAEhC,oBACExB,MAAA,CAAAS,OAAA,CAAAiB,aAAA,CAACjC,wBAAwB,EAAAiB,QAAA,KACnB6B,IAAI;IACRL,IAAI,EAAC,MAAM;IACXL,KAAK,EAAE,CAACC,MAAM,CAACK,aAAa,EAAEN,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAACtC,OAAA,CAAAK,yBAAA,GAAAA,yBAAA;AAEK,MAAME,2BAA2B,GAAI0B,KAAgB,IAAkB;EAC5E,MAAM;IAAEK,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGf,KAAK;EAEhC,oBACExB,MAAA,CAAAS,OAAA,CAAAiB,aAAA,CAACjC,wBAAwB,EAAAiB,QAAA,KACnB6B,IAAI;IACRL,IAAI,EAAC,QAAQ;IACbL,KAAK,EAAE,CAACC,MAAM,CAACU,mBAAmB,EAAEX,KAAK;EAAE,EAC5C,CAAC;AAEN,CAAC;AAACtC,OAAA,CAAAO,2BAAA,GAAAA,2BAAA;AAEK,MAAMJ,8BAA8B,GACzC8B,KAAgB,iBAEhBxB,MAAA,CAAAS,OAAA,CAAAiB,aAAA,CAACjC,wBAAwB,EAAAiB,QAAA,KACnBc,KAAK;EACTU,IAAI,EAAC,WAAW;EAChBL,KAAK,EAAEC,MAAM,CAACK;AAAc,EAC7B,CACF;AAAC5C,OAAA,CAAAG,8BAAA,GAAAA,8BAAA;AAEF,MAAMoC,MAAM,GAAGW,uBAAU,CAACC,MAAM,CAAC;EAC/BP,aAAa,EAAE;IACbQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDL,mBAAmB,EAAE;IACnBG,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDf,YAAY,EAAE;IACZgB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,MAAM;IACbL,aAAa,EAAE,KAAK;IACpBE,cAAc,EAAE,eAAe;IAC/B;IACA;IACAD,UAAU,EAAEK,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,QAAQ,GAAGC;EACjD;AACF,CAAC,CAAC", "ignoreList": []}