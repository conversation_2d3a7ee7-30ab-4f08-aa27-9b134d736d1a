{"version": 3, "names": ["React", "processColor", "StyleSheet", "View", "RNCPickerNativeComponent", "PickerMacOSItem", "props", "PickerMacOS", "Component", "_picker", "state", "selectedIndex", "items", "<PERSON><PERSON>", "getDerivedStateFromProps", "Children", "toArray", "children", "for<PERSON>ach", "child", "index", "value", "selected<PERSON><PERSON><PERSON>", "push", "label", "textColor", "color", "testID", "render", "createElement", "style", "ref", "picker", "styles", "pickerMacOS", "itemStyle", "onChange", "_onChange", "event", "onValueChange", "nativeEvent", "newValue", "newIndex", "setNativeProps", "create", "height"], "sourceRoot": "../../js", "sources": ["PickerMacOS.macos.js"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAQC,YAAY,EAAEC,UAAU,EAAEC,IAAI,QAAO,cAAc;AAC3D,OAAOC,wBAAwB,MAAM,4BAA4B;AA6DjE,MAAMC,eAAe,GAAIC,KAAgB,IAAW;EAClD,OAAO,IAAI;AACb,CAAC;AAED,MAAMC,WAAW,SAASP,KAAK,CAACQ,SAAS,CAAe;EACtDC,OAAO,GAAoC,IAAI;EAE/CC,KAAK,GAAU;IACbC,aAAa,EAAE,CAAC;IAChBC,KAAK,EAAE;EACT,CAAC;EAED,OAAOC,IAAI,GAA2BR,eAAe;EAErD,OAAOS,wBAAwBA,CAACR,KAAY,EAAS;IACnD,IAAIK,aAAa,GAAG,CAAC;IACrB,MAAMC,KAAK,GAAG,EAAE;IAChBZ,KAAK,CAACe,QAAQ,CAACC,OAAO,CAAaV,KAAK,CAACW,QAAQ,CAAC,CAACC,OAAO,CAAC,UACzDC,KAAiB,EACjBC,KAAa,EACb;MACA,IAAID,KAAK,CAACb,KAAK,CAACe,KAAK,KAAKf,KAAK,CAACgB,aAAa,EAAE;QAC7CX,aAAa,GAAGS,KAAK;MACvB;MACAR,KAAK,CAACW,IAAI,CAAC;QACTF,KAAK,EAAEF,KAAK,CAACb,KAAK,CAACe,KAAK;QACxBG,KAAK,EAAEL,KAAK,CAACb,KAAK,CAACkB,KAAK;QACxBC,SAAS,EAAExB,YAAY,CAACkB,KAAK,CAACb,KAAK,CAACoB,KAAK,CAAC;QAC1CC,MAAM,EAAER,KAAK,CAACb,KAAK,CAACqB;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO;MAAChB,aAAa;MAAEC;IAAK,CAAC;EAC/B;EAEAgB,MAAMA,CAAA,EAAe;IACnB,oBACE5B,KAAA,CAAA6B,aAAA,CAAC1B,IAAI;MAAC2B,KAAK,EAAE,IAAI,CAACxB,KAAK,CAACwB;IAAM,gBAC5B9B,KAAA,CAAA6B,aAAA,CAACzB,wBAAwB;MACvB2B,GAAG,EAAGC,MAAM,IAAK;QACf,IAAI,CAACvB,OAAO,GAAGuB,MAAM;MACvB,CAAE;MACFL,MAAM,EAAE,IAAI,CAACrB,KAAK,CAACqB,MAAO;MAC1BG,KAAK,EAAE,CAACG,MAAM,CAACC,WAAW,EAAE,IAAI,CAAC5B,KAAK,CAAC6B,SAAS;MAChD;MAAA;MACAvB,KAAK,EAAE,IAAI,CAACF,KAAK,CAACE,KAAM;MACxBD,aAAa,EAAE,IAAI,CAACD,KAAK,CAACC,aAAc;MACxCyB,QAAQ,EAAE,IAAI,CAACC;IAAU,CAC1B,CACG,CAAC;EAEX;EAEAA,SAAS,GAAIC,KAAiB,IAAK;IACjC,IAAI,IAAI,CAAChC,KAAK,CAAC8B,QAAQ,EAAE;MACvB,IAAI,CAAC9B,KAAK,CAAC8B,QAAQ,CAACE,KAAK,CAAC;IAC5B;IACA,IAAI,IAAI,CAAChC,KAAK,CAACiC,aAAa,EAAE;MAC5B,IAAI,CAACjC,KAAK,CAACiC,aAAa,CACtBD,KAAK,CAACE,WAAW,CAACC,QAAQ,EAC1BH,KAAK,CAACE,WAAW,CAACE,QACpB,CAAC;IACH;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,IACE,IAAI,CAACjC,OAAO,IACZ,IAAI,CAACC,KAAK,CAACC,aAAa,KAAK2B,KAAK,CAACE,WAAW,CAACE,QAAQ,EACvD;MACA,IAAI,CAACjC,OAAO,CAACkC,cAAc,CAAC;QAC1BhC,aAAa,EAAE,IAAI,CAACD,KAAK,CAACC;MAC5B,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AAEA,MAAMsB,MAAM,GAAG/B,UAAU,CAAC0C,MAAM,CAAC;EAC/BV,WAAW,EAAE;IACX;IACA;IACA;IACAW,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAetC,WAAW"}