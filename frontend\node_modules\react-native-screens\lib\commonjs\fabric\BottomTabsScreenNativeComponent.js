"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _codegenNativeComponent = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
// @ts-ignore: ImageSource type has been recently added: https://github.com/facebook/react-native/pull/51969
// iOS-specific: SFSymbol, image as a template usage
// eslint-disable-next-line @typescript-eslint/ban-types
var _default = exports.default = (0, _codegenNativeComponent.default)('RNSBottomTabsScreen', {});
//# sourceMappingURL=BottomTabsScreenNativeComponent.js.map