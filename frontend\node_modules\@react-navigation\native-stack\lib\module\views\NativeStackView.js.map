{"version": 3, "names": ["getHeaderTitle", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderBackContext", "SafeAreaProviderCompat", "Screen", "useHeaderHeight", "useLinkBuilder", "React", "Animated", "Image", "StyleSheet", "View", "AnimatedHeaderHeightContext", "jsx", "_jsx", "TRANSPARENT_PRESENTATIONS", "NativeStackView", "state", "descriptors", "describe", "parentHeaderBack", "useContext", "buildHref", "preloadedDescriptors", "preloadedRoutes", "reduce", "acc", "route", "key", "children", "routes", "concat", "map", "i", "isFocused", "index", "previousKey", "<PERSON><PERSON><PERSON>", "previousDescriptor", "undefined", "nextDescriptor", "options", "navigation", "render", "headerBack", "title", "name", "href", "params", "canGoBack", "header", "headerShown", "headerBackImageSource", "headerLeft", "headerTransparent", "headerBackTitle", "presentation", "contentStyle", "rest", "nextPresentation", "isPreloaded", "focused", "back", "label", "tintColor", "backImage", "source", "resizeMode", "style", "styles", "onPress", "goBack", "absoluteFill", "display", "includes", "backgroundColor", "Provider", "value", "AnimatedHeaderHeightProvider", "contentContainer", "headerHeight", "animatedHeaderHeight", "useState", "Value", "useEffect", "setValue", "create", "flex", "height", "width", "margin"], "sourceRoot": "../../../src", "sources": ["views/NativeStackView.tsx"], "mappings": ";;AAAA,SACEA,cAAc,EACdC,MAAM,EACNC,gBAAgB,EAChBC,iBAAiB,EACjBC,sBAAsB,EACtBC,MAAM,EACNC,eAAe,QACV,4BAA4B;AACnC,SAIEC,cAAc,QACT,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAOhE,SAASC,2BAA2B,QAAQ,qCAAkC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAa/E,MAAMC,yBAAyB,GAAG,CAChC,kBAAkB,EAClB,2BAA2B,CAC5B;AAED,OAAO,SAASC,eAAeA,CAAC;EAAEC,KAAK;EAAEC,WAAW;EAAEC;AAAgB,CAAC,EAAE;EACvE,MAAMC,gBAAgB,GAAGb,KAAK,CAACc,UAAU,CAACnB,iBAAiB,CAAC;EAC5D,MAAM;IAAEoB;EAAU,CAAC,GAAGhB,cAAc,CAAC,CAAC;EAEtC,MAAMiB,oBAAoB,GACxBN,KAAK,CAACO,eAAe,CAACC,MAAM,CAA2B,CAACC,GAAG,EAAEC,KAAK,KAAK;IACrED,GAAG,CAACC,KAAK,CAACC,GAAG,CAAC,GAAGF,GAAG,CAACC,KAAK,CAACC,GAAG,CAAC,IAAIT,QAAQ,CAACQ,KAAK,EAAE,IAAI,CAAC;IACxD,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAER,oBACEZ,IAAA,CAACX,sBAAsB;IAAA0B,QAAA,EACpBZ,KAAK,CAACa,MAAM,CAACC,MAAM,CAACd,KAAK,CAACO,eAAe,CAAC,CAACQ,GAAG,CAAC,CAACL,KAAK,EAAEM,CAAC,KAAK;MAC5D,MAAMC,SAAS,GAAGjB,KAAK,CAACkB,KAAK,KAAKF,CAAC;MACnC,MAAMG,WAAW,GAAGnB,KAAK,CAACa,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,EAAEL,GAAG;MAC5C,MAAMS,OAAO,GAAGpB,KAAK,CAACa,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,EAAEL,GAAG;MACxC,MAAMU,kBAAkB,GAAGF,WAAW,GAClClB,WAAW,CAACkB,WAAW,CAAC,GACxBG,SAAS;MACb,MAAMC,cAAc,GAAGH,OAAO,GAAGnB,WAAW,CAACmB,OAAO,CAAC,GAAGE,SAAS;MACjE,MAAM;QAAEE,OAAO;QAAEC,UAAU;QAAEC;MAAO,CAAC,GACnCzB,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,IAAIL,oBAAoB,CAACI,KAAK,CAACC,GAAG,CAAC;MAE3D,MAAMgB,UAAU,GAAGN,kBAAkB,GACjC;QACEO,KAAK,EAAE9C,cAAc,CACnBuC,kBAAkB,CAACG,OAAO,EAC1BH,kBAAkB,CAACX,KAAK,CAACmB,IAC3B,CAAC;QACDC,IAAI,EAAEzB,SAAS,CACbgB,kBAAkB,CAACX,KAAK,CAACmB,IAAI,EAC7BR,kBAAkB,CAACX,KAAK,CAACqB,MAC3B;MACF,CAAC,GACD5B,gBAAgB;MAEpB,MAAM6B,SAAS,GAAGL,UAAU,IAAI,IAAI;MAEpC,MAAM;QACJM,MAAM;QACNC,WAAW;QACXC,qBAAqB;QACrBC,UAAU;QACVC,iBAAiB;QACjBC,eAAe;QACfC,YAAY;QACZC,YAAY;QACZ,GAAGC;MACL,CAAC,GAAGjB,OAAO;MAEX,MAAMkB,gBAAgB,GAAGnB,cAAc,EAAEC,OAAO,CAACe,YAAY;MAE7D,MAAMI,WAAW,GACfrC,oBAAoB,CAACI,KAAK,CAACC,GAAG,CAAC,KAAKW,SAAS,IAC7CrB,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,KAAKW,SAAS;MAEtC,oBACEzB,IAAA,CAACV,MAAM;QAELyD,OAAO,EAAE3B,SAAU;QACnBP,KAAK,EAAEA,KAAM;QACbe,UAAU,EAAEA,UAAW;QACvBS,WAAW,EAAEA,WAAY;QACzBG,iBAAiB,EAAEA,iBAAkB;QACrCJ,MAAM,EACJA,MAAM,KAAKX,SAAS,GAClBW,MAAM,CAAC;UACLY,IAAI,EAAElB,UAAU;UAChBH,OAAO;UACPd,KAAK;UACLe;QACF,CAAC,CAAC,gBAEF5B,IAAA,CAACd,MAAM;UAAA,GACD0D,IAAI;UACRI,IAAI,EAAElB,UAAW;UACjBC,KAAK,EAAE9C,cAAc,CAAC0C,OAAO,EAAEd,KAAK,CAACmB,IAAI,CAAE;UAC3CO,UAAU,EACR,OAAOA,UAAU,KAAK,UAAU,GAC5B,CAAC;YAAEU,KAAK;YAAE,GAAGL;UAAK,CAAC,KACjBL,UAAU,CAAC;YACT,GAAGK,IAAI;YACPK,KAAK,EAAER,eAAe,IAAIQ;UAC5B,CAAC,CAAC,GACJV,UAAU,KAAKd,SAAS,IAAIU,SAAS,GACnC,CAAC;YAAEe,SAAS;YAAED,KAAK;YAAE,GAAGL;UAAK,CAAC,kBAC5B5C,IAAA,CAACb,gBAAgB;YAAA,GACXyD,IAAI;YACRK,KAAK,EAAER,eAAe,IAAIQ,KAAM;YAChCC,SAAS,EAAEA,SAAU;YACrBC,SAAS,EACPb,qBAAqB,KAAKb,SAAS,GAC/B,mBACEzB,IAAA,CAACL,KAAK;cACJyD,MAAM,EAAEd,qBAAsB;cAC9Be,UAAU,EAAC,SAAS;cACpBH,SAAS,EAAEA,SAAU;cACrBI,KAAK,EAAEC,MAAM,CAACJ;YAAU,CACzB,CACF,GACD1B,SACL;YACD+B,OAAO,EAAE5B,UAAU,CAAC6B;UAAO,CAC5B,CACF,GACDlB,UACP;UACDC,iBAAiB,EAAEA;QAAkB,CACtC,CAEJ;QACDc,KAAK,EAAE,CACL1D,UAAU,CAAC8D,YAAY,EACvB;UACEC,OAAO,EACL,CAACvC,SAAS,IACPyB,gBAAgB,IAAI,IAAI,IACvB5C,yBAAyB,CAAC2D,QAAQ,CAACf,gBAAgB,CAAE,KACzD,CAACC,WAAW,GACR,MAAM,GACN;QACR,CAAC,EACDJ,YAAY,IAAI,IAAI,IACpBzC,yBAAyB,CAAC2D,QAAQ,CAAClB,YAAY,CAAC,GAC5C;UAAEmB,eAAe,EAAE;QAAc,CAAC,GAClC,IAAI,CACR;QAAA9C,QAAA,eAEFf,IAAA,CAACZ,iBAAiB,CAAC0E,QAAQ;UAACC,KAAK,EAAEjC,UAAW;UAAAf,QAAA,eAC5Cf,IAAA,CAACgE,4BAA4B;YAAAjD,QAAA,eAC3Bf,IAAA,CAACH,IAAI;cAACyD,KAAK,EAAE,CAACC,MAAM,CAACU,gBAAgB,EAAEtB,YAAY,CAAE;cAAA5B,QAAA,EAClDc,MAAM,CAAC;YAAC,CACL;UAAC,CACqB;QAAC,CACL;MAAC,GA5ExBhB,KAAK,CAACC,GA6EL,CAAC;IAEb,CAAC;EAAC,CACoB,CAAC;AAE7B;AAEA,MAAMkD,4BAA4B,GAAGA,CAAC;EACpCjD;AAGF,CAAC,KAAK;EACJ,MAAMmD,YAAY,GAAG3E,eAAe,CAAC,CAAC;EACtC,MAAM,CAAC4E,oBAAoB,CAAC,GAAG1E,KAAK,CAAC2E,QAAQ,CAC3C,MAAM,IAAI1E,QAAQ,CAAC2E,KAAK,CAACH,YAAY,CACvC,CAAC;EAEDzE,KAAK,CAAC6E,SAAS,CAAC,MAAM;IACpBH,oBAAoB,CAACI,QAAQ,CAACL,YAAY,CAAC;EAC7C,CAAC,EAAE,CAACC,oBAAoB,EAAED,YAAY,CAAC,CAAC;EAExC,oBACElE,IAAA,CAACF,2BAA2B,CAACgE,QAAQ;IAACC,KAAK,EAAEI,oBAAqB;IAAApD,QAAA,EAC/DA;EAAQ,CAC2B,CAAC;AAE3C,CAAC;AAED,MAAMwC,MAAM,GAAG3D,UAAU,CAAC4E,MAAM,CAAC;EAC/BP,gBAAgB,EAAE;IAChBQ,IAAI,EAAE;EACR,CAAC;EACDtB,SAAS,EAAE;IACTuB,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}